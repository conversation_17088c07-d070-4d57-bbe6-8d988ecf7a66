<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡片轮播测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        }
        
        #app {
            width: 100vw;
            height: 100vh;
        }
    </style>
</head>
<body>
    <div id="app">
        <card-carousel></card-carousel>
    </div>

    <script>
        const { createApp } = Vue;

        const CardCarousel = {
            template: `
                <div class="app-container">
                    <!-- 卡片轮播页面 -->
                    <div v-if="!showEditor" class="carousel-container">
                        <div class="carousel-wrapper">
                            <div 
                                class="carousel-track"
                                :style="{ transform: \`translateY(\${translateY}px)\` }"
                                @touchstart="handleTouchStart"
                                @touchmove="handleTouchMove"
                                @touchend="handleTouchEnd"
                            >
                                <div
                                    v-for="(card, index) in displayCards"
                                    :key="card.id"
                                    class="card"
                                    :class="getCardClass(index)"
                                    @click="handleCardClick(card, index)"
                                >
                                    <div class="card-content">
                                        <h3>{{ card.title }}</h3>
                                        <p>{{ card.description }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 指示器 -->
                        <div class="indicators">
                            <div
                                v-for="(card, index) in cards"
                                :key="card.id"
                                class="indicator"
                                :class="{ active: index === currentIndex }"
                            ></div>
                        </div>
                    </div>

                    <!-- 文字编辑页面 -->
                    <div v-if="showEditor" class="editor-container">
                        <div class="editor-header">
                            <button class="back-btn" @click="goBack">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <h2>{{ selectedCard?.title }}</h2>
                            <button class="save-btn" @click="saveContent">保存</button>
                        </div>
                        
                        <div class="editor-content">
                            <textarea
                                v-model="editorText"
                                placeholder="在这里输入你的内容..."
                                class="text-editor"
                            ></textarea>
                        </div>
                    </div>
                </div>
            `,
            data() {
                return {
                    cards: [
                        { id: 1, title: '学习指南', description: '新生学习相关指导', content: '' },
                        { id: 2, title: '生活指南', description: '校园生活实用信息', content: '' },
                        { id: 3, title: '社团活动', description: '丰富多彩的社团生活', content: '' },
                        { id: 4, title: '学术资源', description: '图书馆与学术支持', content: '' },
                        { id: 5, title: '就业指导', description: '职业规划与就业服务', content: '' },
                        { id: 6, title: '校园服务', description: '各类校园便民服务', content: '' }
                    ],
                    currentIndex: 0,
                    translateY: 0,
                    showEditor: false,
                    selectedCard: null,
                    editorText: '',
                    
                    // 触摸相关
                    touchStartY: 0,
                    touchStartTime: 0,
                    isDragging: false,
                    
                    // 动画相关
                    isAnimating: false,
                    cardHeight: 120,
                    cardSpacing: 20
                }
            },
            computed: {
                displayCards() {
                    // 创建循环显示的卡片数组，前后各添加2个卡片以实现无缝循环
                    const result = []
                    const totalCards = this.cards.length
                    
                    for (let i = -2; i < totalCards + 2; i++) {
                        let index = i
                        if (index < 0) index = totalCards + index
                        if (index >= totalCards) index = index - totalCards
                        
                        result.push({
                            ...this.cards[index],
                            displayIndex: i
                        })
                    }
                    
                    return result
                }
            },
            mounted() {
                this.updateTranslateY()
            },
            methods: {
                getCardClass(displayIndex) {
                    const actualIndex = displayIndex - 2 // 因为前面添加了2个卡片
                    const distance = Math.abs(actualIndex - this.currentIndex)
                    
                    if (actualIndex === this.currentIndex) {
                        return 'card-center'
                    } else if (distance === 1) {
                        return 'card-side'
                    } else {
                        return 'card-far'
                    }
                },
                
                updateTranslateY() {
                    // 计算当前应该的translateY值
                    const centerOffset = 2 * (this.cardHeight + this.cardSpacing) // 前面2个卡片的偏移
                    const currentOffset = this.currentIndex * (this.cardHeight + this.cardSpacing)
                    this.translateY = -(centerOffset + currentOffset)
                },
                
                handleTouchStart(e) {
                    if (this.isAnimating) return
                    
                    this.touchStartY = e.touches[0].clientY
                    this.touchStartTime = Date.now()
                    this.isDragging = true
                },
                
                handleTouchMove(e) {
                    if (!this.isDragging || this.isAnimating) return
                    
                    e.preventDefault()
                    const currentY = e.touches[0].clientY
                    const deltaY = currentY - this.touchStartY
                    
                    // 实时更新位置，但限制拖拽距离
                    const maxDrag = this.cardHeight + this.cardSpacing
                    const limitedDelta = Math.max(-maxDrag, Math.min(maxDrag, deltaY))
                    
                    const centerOffset = 2 * (this.cardHeight + this.cardSpacing)
                    const currentOffset = this.currentIndex * (this.cardHeight + this.cardSpacing)
                    this.translateY = -(centerOffset + currentOffset) + limitedDelta
                },
                
                handleTouchEnd(e) {
                    if (!this.isDragging || this.isAnimating) return

                    this.isDragging = false
                    const currentY = e.changedTouches[0].clientY
                    const deltaY = currentY - this.touchStartY
                    const deltaTime = Date.now() - this.touchStartTime

                    // 判断是否需要切换卡片
                    const threshold = 50 // 滑动阈值
                    const velocity = Math.abs(deltaY) / deltaTime // 滑动速度

                    if (Math.abs(deltaY) > threshold || velocity > 0.5) {
                        if (deltaY < 0) {
                            // 向上滑动，显示下一张
                            this.nextCard()
                        } else {
                            // 向下滑动，显示上一张
                            this.prevCard()
                        }
                    } else {
                        // 回弹到当前位置
                        this.updateTranslateY()
                    }
                },

                nextCard() {
                    if (this.isAnimating) return

                    this.isAnimating = true
                    this.currentIndex = (this.currentIndex + 1) % this.cards.length
                    this.updateTranslateY()

                    setTimeout(() => {
                        this.isAnimating = false
                    }, 300)
                },

                prevCard() {
                    if (this.isAnimating) return

                    this.isAnimating = true
                    this.currentIndex = this.currentIndex === 0 ? this.cards.length - 1 : this.currentIndex - 1
                    this.updateTranslateY()

                    setTimeout(() => {
                        this.isAnimating = false
                    }, 300)
                },
                
                handleCardClick(card, displayIndex) {
                    const actualIndex = displayIndex - 2

                    if (actualIndex === this.currentIndex) {
                        // 点击中心卡片，进入编辑页面
                        this.selectedCard = this.cards.find(c => c.id === card.id) // 确保获取原始卡片
                        this.editorText = this.selectedCard.content || ''
                        this.showEditor = true
                    } else {
                        // 点击其他卡片，切换到该卡片
                        // 计算最短路径
                        let targetIndex = actualIndex
                        if (targetIndex < 0) targetIndex += this.cards.length
                        if (targetIndex >= this.cards.length) targetIndex -= this.cards.length

                        // 计算向前和向后的距离
                        const forwardDistance = (targetIndex - this.currentIndex + this.cards.length) % this.cards.length
                        const backwardDistance = (this.currentIndex - targetIndex + this.cards.length) % this.cards.length

                        // 选择最短路径
                        if (forwardDistance <= backwardDistance) {
                            // 向前移动
                            for (let i = 0; i < forwardDistance; i++) {
                                setTimeout(() => this.nextCard(), i * 100)
                            }
                        } else {
                            // 向后移动
                            for (let i = 0; i < backwardDistance; i++) {
                                setTimeout(() => this.prevCard(), i * 100)
                            }
                        }
                    }
                },
                
                goBack() {
                    this.showEditor = false
                    this.selectedCard = null
                },
                
                saveContent() {
                    if (this.selectedCard) {
                        // 找到对应的卡片并更新内容
                        const cardIndex = this.cards.findIndex(card => card.id === this.selectedCard.id)
                        if (cardIndex !== -1) {
                            this.cards[cardIndex].content = this.editorText
                        }
                    }
                    this.goBack()
                }
            }
        }

        createApp({
            components: {
                CardCarousel
            }
        }).mount('#app')
    </script>

    <style>
        .app-container {
            width: 100%;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            position: relative;
        }

        /* 轮播容器 */
        .carousel-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
        }

        .carousel-wrapper {
            width: 100%;
            max-width: 350px;
            height: 500px;
            position: relative;
            overflow: hidden;
        }

        .carousel-track {
            width: 100%;
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            will-change: transform;
        }

        /* 卡片样式 */
        .card {
            width: 100%;
            margin-bottom: 20px;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            transform-origin: center;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card-center {
            height: 120px;
            transform: scale(1);
            opacity: 1;
            z-index: 3;
        }

        .card-center::before {
            opacity: 1;
        }

        .card-side {
            height: 70px;
            transform: scale(0.85);
            opacity: 0.6;
            z-index: 2;
        }

        .card-far {
            height: 50px;
            transform: scale(0.7);
            opacity: 0.3;
            z-index: 1;
        }

        .card-content {
            padding: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }

        .card-content h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            transition: font-size 0.3s ease;
        }

        .card-side .card-content h3,
        .card-far .card-content h3 {
            font-size: 16px;
        }

        .card-content p {
            margin: 0;
            font-size: 14px;
            color: #666;
            line-height: 1.4;
            transition: font-size 0.3s ease;
        }

        .card-side .card-content p,
        .card-far .card-content p {
            font-size: 12px;
        }

        /* 指示器 */
        .indicators {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 30px;
        }

        .indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .indicator.active {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.2);
        }

        /* 编辑器页面 */
        .editor-container {
            width: 100%;
            height: 100vh;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
        }

        .editor-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 10;
        }

        .back-btn {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            color: #667eea;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .editor-header h2 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .save-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .save-btn:hover {
            background: #5a67d8;
        }

        .editor-content {
            flex: 1;
            padding: 20px;
            overflow: hidden;
        }

        .text-editor {
            width: 100%;
            height: 100%;
            border: none;
            outline: none;
            resize: none;
            font-size: 16px;
            line-height: 1.6;
            color: #333;
            background: transparent;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .text-editor::placeholder {
            color: #999;
        }

        /* 手机端适配 */
        @media (max-width: 480px) {
            .carousel-container {
                padding: 15px;
            }

            .carousel-wrapper {
                max-width: 320px;
                height: 450px;
            }

            .card-center {
                height: 100px;
            }

            .card-side {
                height: 60px;
            }

            .card-far {
                height: 40px;
            }

            .card-content {
                padding: 15px;
            }

            .card-content h3 {
                font-size: 16px;
            }

            .card-side .card-content h3,
            .card-far .card-content h3 {
                font-size: 14px;
            }

            .card-content p {
                font-size: 13px;
            }

            .card-side .card-content p,
            .card-far .card-content p {
                font-size: 11px;
            }

            .editor-header {
                padding: 12px 15px;
            }

            .editor-header h2 {
                font-size: 16px;
            }

            .editor-content {
                padding: 15px;
            }

            .text-editor {
                font-size: 15px;
            }
        }

        /* 触摸反馈 */
        .card:active {
            transform: scale(0.98);
        }

        .card-center:active {
            transform: scale(0.98);
        }

        /* 滚动条样式 */
        .text-editor::-webkit-scrollbar {
            width: 6px;
        }

        .text-editor::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .text-editor::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .text-editor::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</body>
</html>
